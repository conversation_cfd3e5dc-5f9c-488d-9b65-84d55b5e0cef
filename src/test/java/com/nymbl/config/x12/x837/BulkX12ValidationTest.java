package com.nymbl.config.x12.x837;

import com.imsweb.x12.Element;
import com.imsweb.x12.Loop;
import com.imsweb.x12.Segment;
import com.imsweb.x12.reader.X12Reader;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive validation tests for bulk X12 claim generation using com.imsweb:x12-parser library.
 * This test validates that bulk X12 files are 100% valid according to professional X12 standards.
 * 
 * This test does NOT require Spring context - it's a pure X12 validation test.
 */
public class BulkX12ValidationTest {

    /**
     * Test validates the x12.txt file in the project root using com.imsweb X12 parser.
     * This ensures the bulk X12 generation creates valid files with no warnings or errors.
     * Provides detailed diagnostic information about any issues found.
     */
    @Test
    public void testValidateX12FileWithImsweb() throws IOException {
        // Read the x12.txt file from project root
        String x12Content = Files.readString(Paths.get("x12.txt"));

        assertNotNull(x12Content, "X12 file content should not be null");
        assertFalse(x12Content.trim().isEmpty(), "X12 file content should not be empty");

        System.out.println("🔍 Starting X12 validation...");
        System.out.println("📄 File size: " + x12Content.length() + " characters");
        
        // Count segments for initial analysis
        String[] segments = x12Content.split("~");
        int segmentCount = segments.length - 1; // Subtract 1 for the final empty element after last ~
        System.out.println("📊 Total segments found: " + segmentCount);

        // Validate using com.imsweb X12 parser - use File constructor as shown in documentation
        try {
            // Write content to a temporary file since X12Reader expects a File
            File tempFile = File.createTempFile("x12_test", ".txt");
            tempFile.deleteOnExit();
            Files.write(tempFile.toPath(), x12Content.getBytes());

            // Use the constructor shown in the GitHub documentation
            X12Reader x12Reader = new X12Reader(X12Reader.FileType.ANSI837_5010_X222, tempFile);

            // Parse the X12 file
            List<Loop> loops = x12Reader.getLoops();

            // Check for any parsing errors
            List<String> errors = x12Reader.getErrors();
            List<String> fatalErrors = x12Reader.getFatalErrors();

            // Detailed error reporting
            System.out.println("\n📋 VALIDATION RESULTS:");
            System.out.println("=" .repeat(50));
            
            if (loops != null && !loops.isEmpty()) {
                System.out.println("✅ Successfully parsed " + loops.size() + " ISA transaction(s)");
                
                // Analyze loop structure
                analyzeLoopStructure(loops);
            } else {
                System.out.println("❌ No ISA transactions were successfully parsed");
            }

            // Report errors in detail
            if (!errors.isEmpty()) {
                System.out.println("\n⚠️  VALIDATION ERRORS FOUND:");
                System.out.println("-".repeat(30));
                for (int i = 0; i < errors.size(); i++) {
                    System.out.println((i + 1) + ". " + errors.get(i));
                }
            }

            if (!fatalErrors.isEmpty()) {
                System.out.println("\n🚨 FATAL ERRORS FOUND:");
                System.out.println("-".repeat(30));
                for (int i = 0; i < fatalErrors.size(); i++) {
                    System.out.println((i + 1) + ". " + fatalErrors.get(i));
                }
            }

            // Analyze segment structure regardless of parsing success
            analyzeSegmentStructure(segments);

            // Provide recommendations
            provideRecommendations(errors, fatalErrors, loops);

            // For debugging purposes, let's not fail the test immediately
            // Instead, collect all information first
            if (!errors.isEmpty() || !fatalErrors.isEmpty()) {
                System.out.println("\n🔧 FACTORY837 FIXES NEEDED:");
                System.out.println("=" .repeat(40));
                System.out.println("Based on the errors above, you should check your Factory837 class for:");
                System.out.println("1. Missing required segments");
                System.out.println("2. Incorrect segment order");
                System.out.println("3. Invalid data formats");
                System.out.println("4. Missing mandatory fields");
                System.out.println("5. Incorrect loop structures");
            }

            System.out.println("\n" + "=".repeat(50));
            
            if (errors.isEmpty() && fatalErrors.isEmpty()) {
                System.out.println("🎉 X12 file validation PASSED - 100% valid with no errors");
            } else {
                System.out.println("📝 X12 file has issues that need to be addressed in Factory837");
                // Fail the test but with helpful information
                fail("X12 validation found " + errors.size() + " errors and " + fatalErrors.size() + " fatal errors. See console output for details.");
            }

        } catch (Exception e) {
            System.out.println("💥 PARSER EXCEPTION: " + e.getMessage());
            e.printStackTrace();
            fail("Failed to parse X12 file with com.imsweb parser: " + e.getMessage(), e);
        }
    }
    
    /**
     * Analyzes the loop structure of successfully parsed X12 data
     */
    private void analyzeLoopStructure(List<Loop> loops) {
        System.out.println("\n🔄 LOOP STRUCTURE ANALYSIS:");
        System.out.println("-".repeat(30));
        
        for (int i = 0; i < loops.size(); i++) {
            Loop isaLoop = loops.get(i);
            System.out.println("ISA Transaction " + (i + 1) + ":");
            
            try {
                // Try to navigate the standard 837 structure
                analyzeISALoop(isaLoop);
            } catch (Exception e) {
                System.out.println("  ❌ Error analyzing ISA loop structure: " + e.getMessage());
            }
        }
    }
    
    /**
     * Analyzes a single ISA loop following 837 structure
     */
    private void analyzeISALoop(Loop isaLoop) {
        // Check for ISA segment
        try {
            Segment isaSegment = isaLoop.getSegment("ISA");
            if (isaSegment != null) {
                System.out.println("  ✅ ISA segment found");
            } else {
                System.out.println("  ❌ ISA segment missing");
            }
        } catch (Exception e) {
            System.out.println("  ❌ Error accessing ISA segment: " + e.getMessage());
        }
        
        // Try to find GS loop
        try {
            Loop gsLoop = isaLoop.getLoop("GS_LOOP");
            if (gsLoop != null) {
                System.out.println("  ✅ GS_LOOP found");
                analyzeGSLoop(gsLoop);
            } else {
                System.out.println("  ❌ GS_LOOP missing");
            }
        } catch (Exception e) {
            System.out.println("  ❌ Error accessing GS_LOOP: " + e.getMessage());
        }
        
        // Check for IEA segment
        try {
            Segment ieaSegment = isaLoop.getSegment("IEA");
            if (ieaSegment != null) {
                System.out.println("  ✅ IEA segment found");
            } else {
                System.out.println("  ❌ IEA segment missing");
            }
        } catch (Exception e) {
            System.out.println("  ❌ Error accessing IEA segment: " + e.getMessage());
        }
    }
    
    /**
     * Analyzes GS loop structure
     */
    private void analyzeGSLoop(Loop gsLoop) {
        try {
            Segment gsSegment = gsLoop.getSegment("GS");
            if (gsSegment != null) {
                System.out.println("    ✅ GS segment found");
            } else {
                System.out.println("    ❌ GS segment missing");
            }
        } catch (Exception e) {
            System.out.println("    ❌ Error accessing GS segment: " + e.getMessage());
        }
        
        // Try to find ST loop
        try {
            Loop stLoop = gsLoop.getLoop("ST_LOOP");
            if (stLoop != null) {
                System.out.println("    ✅ ST_LOOP found");
                analyzeSTLoop(stLoop);
            } else {
                System.out.println("    ❌ ST_LOOP missing");
            }
        } catch (Exception e) {
            System.out.println("    ❌ Error accessing ST_LOOP: " + e.getMessage());
        }
        
        try {
            Segment geSegment = gsLoop.getSegment("GE");
            if (geSegment != null) {
                System.out.println("    ✅ GE segment found");
            } else {
                System.out.println("    ❌ GE segment missing");
            }
        } catch (Exception e) {
            System.out.println("    ❌ Error accessing GE segment: " + e.getMessage());
        }
    }
    
    /**
     * Analyzes ST loop structure for 837 claims
     */
    private void analyzeSTLoop(Loop stLoop) {
        try {
            Segment stSegment = stLoop.getSegment("ST");
            if (stSegment != null) {
                System.out.println("      ✅ ST segment found");
            } else {
                System.out.println("      ❌ ST segment missing");
            }
        } catch (Exception e) {
            System.out.println("      ❌ Error accessing ST segment: " + e.getMessage());
        }
        
        try {
            Segment bhtSegment = stLoop.getSegment("BHT");
            if (bhtSegment != null) {
                System.out.println("      ✅ BHT segment found");
            } else {
                System.out.println("      ❌ BHT segment missing");
            }
        } catch (Exception e) {
            System.out.println("      ❌ Error accessing BHT segment: " + e.getMessage());
        }
        
        // Look for claim loops (2000A, 2000B, etc.)
        try {
            List<Loop> claimLoops = stLoop.findLoop("2000A");
            if (claimLoops != null && !claimLoops.isEmpty()) {
                System.out.println("      ✅ Found " + claimLoops.size() + " billing provider loop(s) (2000A)");
                
                for (Loop claimLoop : claimLoops) {
                    analyzeClaimLoop(claimLoop);
                }
            } else {
                System.out.println("      ❌ No billing provider loops (2000A) found");
            }
        } catch (Exception e) {
            System.out.println("      ❌ Error accessing claim loops: " + e.getMessage());
        }
        
        try {
            Segment seSegment = stLoop.getSegment("SE");
            if (seSegment != null) {
                System.out.println("      ✅ SE segment found");
            } else {
                System.out.println("      ❌ SE segment missing");
            }
        } catch (Exception e) {
            System.out.println("      ❌ Error accessing SE segment: " + e.getMessage());
        }
    }
    
    /**
     * Analyzes individual claim loops
     */
    private void analyzeClaimLoop(Loop claimLoop) {
        try {
            // Look for subscriber loops (2000B)
            List<Loop> subscriberLoops = claimLoop.findLoop("2000B");
            if (subscriberLoops != null && !subscriberLoops.isEmpty()) {
                System.out.println("        ✅ Found " + subscriberLoops.size() + " subscriber loop(s) (2000B)");
                
                for (Loop subLoop : subscriberLoops) {
                    // Look for claim information loops (2300)
                    List<Loop> claimInfoLoops = subLoop.findLoop("2300");
                    if (claimInfoLoops != null && !claimInfoLoops.isEmpty()) {
                        System.out.println("          ✅ Found " + claimInfoLoops.size() + " claim info loop(s) (2300)");
                        
                        for (Loop claimInfo : claimInfoLoops) {
                            // Look for CLM segment
                            try {
                                Segment clmSegment = claimInfo.getSegment("CLM");
                                if (clmSegment != null) {
                                    System.out.println("            ✅ CLM segment found");
                                } else {
                                    System.out.println("            ❌ CLM segment missing");
                                }
                            } catch (Exception e) {
                                System.out.println("            ❌ Error accessing CLM segment: " + e.getMessage());
                            }
                        }
                    } else {
                        System.out.println("          ❌ No claim info loops (2300) found");
                    }
                }
            } else {
                System.out.println("        ❌ No subscriber loops (2000B) found");
            }
        } catch (Exception e) {
            System.out.println("        ❌ Error analyzing claim loop: " + e.getMessage());
        }
    }
    
    /**
     * Analyzes the raw segment structure
     */
    private void analyzeSegmentStructure(String[] segments) {
        System.out.println("\n📝 SEGMENT STRUCTURE ANALYSIS:");
        System.out.println("-".repeat(30));
        
        // Count different segment types
        java.util.Map<String, Integer> segmentCounts = new java.util.HashMap<>();
        
        for (String segment : segments) {
            if (segment.trim().isEmpty()) continue;
            
            String segmentTag = segment.split("\\*")[0];
            segmentCounts.put(segmentTag, segmentCounts.getOrDefault(segmentTag, 0) + 1);
        }
        
        System.out.println("Segment type counts:");
        segmentCounts.entrySet().stream()
            .sorted(java.util.Map.Entry.<String, Integer>comparingByValue().reversed())
            .forEach(entry -> System.out.println("  " + entry.getKey() + ": " + entry.getValue()));
            
        // Check for required segments
        String[] requiredSegments = {"ISA", "GS", "ST", "BHT", "SE", "GE", "IEA"};
        System.out.println("\nRequired segments check:");
        for (String required : requiredSegments) {
            if (segmentCounts.containsKey(required)) {
                System.out.println("  ✅ " + required + " found (" + segmentCounts.get(required) + ")");
            } else {
                System.out.println("  ❌ " + required + " MISSING");
            }
        }
        
        // Check for claim-specific segments
        String[] claimSegments = {"CLM", "NM1", "N3", "N4", "DMG", "SBR", "PRV", "REF"};
        System.out.println("\nClaim-related segments:");
        for (String claimSeg : claimSegments) {
            if (segmentCounts.containsKey(claimSeg)) {
                System.out.println("  ✅ " + claimSeg + " found (" + segmentCounts.get(claimSeg) + ")");
            } else {
                System.out.println("  ❌ " + claimSeg + " not found");
            }
        }
    }
    
    /**
     * Provides specific recommendations based on errors found
     */
    private void provideRecommendations(List<String> errors, List<String> fatalErrors, List<Loop> loops) {
        System.out.println("\n💡 RECOMMENDATIONS FOR FACTORY837:");
        System.out.println("-".repeat(30));
        
        if (fatalErrors.isEmpty() && errors.isEmpty()) {
            System.out.println("✅ No issues found - your Factory837 is generating valid X12!");
            return;
        }
        
        // Generic recommendations based on common issues
        if (!fatalErrors.isEmpty()) {
            System.out.println("🚨 CRITICAL ISSUES (Fatal Errors):");
            System.out.println("  - Check that all required segments are present");
            System.out.println("  - Verify segment order follows X12 837 specification");
            System.out.println("  - Ensure control numbers match between header/trailer segments");
        }
        
        if (!errors.isEmpty()) {
            System.out.println("⚠️  VALIDATION ISSUES (Errors):");
            System.out.println("  - Check data formats (dates, amounts, codes)");
            System.out.println("  - Verify required fields are populated");
            System.out.println("  - Check field lengths and data types");
            System.out.println("  - Validate code values against X12 specifications");
        }
        
        if (loops == null || loops.isEmpty()) {
            System.out.println("🔄 STRUCTURE ISSUES:");
            System.out.println("  - File may not be parsing as valid X12 structure");
            System.out.println("  - Check segment terminators and separators");
            System.out.println("  - Verify ISA segment format");
        }
    }

    /**
     * Test that focuses on specific healthcare claim validation
     */
    @Test
    public void testHealthcareClaimSpecificValidation() throws IOException {
        String x12Content = Files.readString(Paths.get("x12.txt"));

        // Write content to a temporary file
        File tempFile = File.createTempFile("x12_test", ".txt");
        tempFile.deleteOnExit();
        Files.write(tempFile.toPath(), x12Content.getBytes());

        try {
            X12Reader x12Reader = new X12Reader(X12Reader.FileType.ANSI837_5010_X222, tempFile);
            List<Loop> loops = x12Reader.getLoops();

            System.out.println("\n🏥 HEALTHCARE CLAIM SPECIFIC VALIDATION:");
            System.out.println("=" .repeat(45));

            boolean foundAnyClaims = false;
            if (loops != null && !loops.isEmpty()) {
                for (Loop isaLoop : loops) {
                    try {
                        Loop gsLoop = isaLoop.getLoop("GS_LOOP");
                        if (gsLoop != null) {
                            Loop stLoop = gsLoop.getLoop("ST_LOOP");
                            if (stLoop != null) {
                                // Look for claim loops
                                List<Loop> providerLoops = stLoop.findLoop("2000A");
                                if (providerLoops != null && !providerLoops.isEmpty()) {
                                    for (Loop providerLoop : providerLoops) {
                                        List<Loop> subscriberLoops = providerLoop.findLoop("2000B");
                                        if (subscriberLoops != null && !subscriberLoops.isEmpty()) {
                                            for (Loop subscriberLoop : subscriberLoops) {
                                                List<Loop> claimLoops = subscriberLoop.findLoop("2300");
                                                if (claimLoops != null && !claimLoops.isEmpty()) {
                                                    foundAnyClaims = true;
                                                    System.out.println("✅ Found " + claimLoops.size() + " claim loop(s) (2300)");
                                                    
                                                    for (Loop claimLoop : claimLoops) {
                                                        try {
                                                            Segment clmSegment = claimLoop.getSegment("CLM");
                                                            if (clmSegment != null) {
                                                                System.out.println("  ✅ CLM segment found in claim loop");
                                                            } else {
                                                                System.out.println("  ❌ CLM segment missing in claim loop");
                                                            }
                                                        } catch (Exception e) {
                                                            System.out.println("  ❌ Error accessing CLM segment: " + e.getMessage());
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        System.out.println("❌ Error navigating claim structure: " + e.getMessage());
                    }
                }
            }
            
            if (!foundAnyClaims) {
                System.out.println("❌ No claim loops (2300) found!");
                System.out.println("🔧 Factory837 should generate proper claim loop structure:");
                System.out.println("   ISA_LOOP -> GS_LOOP -> ST_LOOP -> 2000A -> 2000B -> 2300 -> CLM");
            }

            // Report any errors specific to healthcare claims
            List<String> errors = x12Reader.getErrors();
            List<String> fatalErrors = x12Reader.getFatalErrors();
            
            if (!errors.isEmpty() || !fatalErrors.isEmpty()) {
                System.out.println("\n🔧 Specific issues to address in Factory837:");
                System.out.println("Review the detailed errors above for specific fixes needed.");
            }

        } catch (Exception e) {
            fail("Healthcare claim validation failed: " + e.getMessage(), e);
        }
    }

    @Test
    public void testCompleteX12StructureValidation() throws IOException {
        System.out.println("\n=".repeat(80));
        System.out.println("🏥 NYMBL BULK X12 CLAIM VALIDATION REPORT");
        System.out.println("=".repeat(80));
        
        // Load the X12 file exported from your UI
        File x12File = new File("x12.txt");
        assertTrue(x12File.exists(), "X12 test file must exist");
        
        System.out.println("📁 File: " + x12File.getAbsolutePath());
        System.out.println("📊 Size: " + x12File.length() + " bytes");
        
        // Check if file is empty
        if (x12File.length() == 0) {
            fail("❌ X12 file is EMPTY! Factory837.buildBulk() is not generating any content.");
        }
        
        // Display file content for analysis
        System.out.println("\n📄 FILE CONTENT ANALYSIS:");
        
        String content = Files.readString(x12File.toPath());
        String[] segments = content.split("~");
        System.out.println("📊 Total segments: " + (segments.length - 1));
        
        // Count CLM segments specifically
        int clmCount = 0;
        for (String segment : segments) {
            if (segment.startsWith("CLM*")) {
                clmCount++;
                System.out.println("✅ Found CLM segment: " + segment.substring(0, Math.min(50, segment.length())) + "...");
            }
        }
        
        System.out.println("\n🎯 CLAIM VALIDATION RESULTS:");
        if (clmCount > 0) {
            System.out.println("✅ SUCCESS: Found " + clmCount + " CLM segment(s)!");
            System.out.println("🎉 Your Factory837 fix is working - bulk claims now include claim information!");
        } else {
            System.out.println("❌ FAILED: No CLM segments found");
            fail("Bulk claims still missing CLM segments");
        }
    }
}